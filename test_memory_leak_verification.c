#include <stdio.h>
#include <stdlib.h>
#include <dsa/array.h>

/**
 * @brief 测试验证动态数组set操作的内存泄漏问题
 * 
 * 这个测试演示了当前array_set函数在动态数组上的内存泄漏问题
 */
void test_dynamic_array_set_memory_leak() {
    printf("=== 测试动态数组set操作的内存泄漏 ===\n");
    
    dsa_array_t* arr = array_create_dynamic(3);
    if (!arr) {
        printf("创建动态数组失败\n");
        return;
    }
    
    // 添加一些动态分配的元素
    int* val1 = malloc(sizeof(int)); *val1 = 100;
    int* val2 = malloc(sizeof(int)); *val2 = 200;
    int* val3 = malloc(sizeof(int)); *val3 = 300;
    
    array_push_back(arr, val1);
    array_push_back(arr, val2);
    array_push_back(arr, val3);
    
    printf("初始数组: ");
    for (size_t i = 0; i < array_size(arr); i++) {
        int* elem = (int*)array_get(arr, i);
        if (elem) printf("%d ", *elem);
    }
    printf("\n");
    
    // 使用array_set替换元素 - 这里会发生内存泄漏！
    printf("\n使用array_set替换索引1的元素...\n");
    int* new_val = malloc(sizeof(int)); *new_val = 999;
    
    // 这个操作会泄漏原来val2指向的内存！
    dsa_array_result_t result = array_set(arr, 1, new_val);
    if (result == ARRAY_SUCCESS) {
        printf("替换成功\n");
    } else {
        printf("替换失败\n");
        free(new_val);
    }
    
    printf("替换后数组: ");
    for (size_t i = 0; i < array_size(arr); i++) {
        int* elem = (int*)array_get(arr, i);
        if (elem) printf("%d ", *elem);
    }
    printf("\n");
    
    printf("\n注意：原来索引1处的值200对应的内存已经泄漏！\n");
    printf("val2指针指向的内存无法再被访问和释放。\n");
    
    // 清理剩余的内存
    array_clear_with_free(arr);
    array_destroy(arr);
    
    printf("\n=== 测试完成 ===\n");
}

/**
 * @brief 对比测试：类型安全函数的正确行为
 */
void test_type_safe_functions_correct_behavior() {
    printf("\n=== 对比：类型安全函数的正确行为 ===\n");
    
    dsa_array_t* arr = array_create_dynamic(3);
    if (!arr) {
        printf("创建动态数组失败\n");
        return;
    }
    
    // 使用类型安全函数添加元素
    array_push_back_int(arr, 100);
    array_push_back_int(arr, 200);
    array_push_back_int(arr, 300);
    
    printf("初始数组: ");
    for (size_t i = 0; i < array_size(arr); i++) {
        int value;
        if (array_get_int(arr, i, &value) == ARRAY_SUCCESS) {
            printf("%d ", value);
        }
    }
    printf("\n");
    
    // 使用array_set_int替换元素 - 这里会正确释放旧内存
    printf("\n使用array_set_int替换索引1的元素...\n");
    dsa_array_result_t result = array_set_int(arr, 1, 999);
    if (result == ARRAY_SUCCESS) {
        printf("替换成功\n");
    } else {
        printf("替换失败\n");
    }
    
    printf("替换后数组: ");
    for (size_t i = 0; i < array_size(arr); i++) {
        int value;
        if (array_get_int(arr, i, &value) == ARRAY_SUCCESS) {
            printf("%d ", value);
        }
    }
    printf("\n");
    
    printf("\n类型安全函数正确释放了旧元素的内存。\n");
    
    // 清理
    array_clear_with_free(arr);
    array_destroy(arr);
    
    printf("\n=== 对比测试完成 ===\n");
}

int main() {
    printf("动态数组set操作内存泄漏验证测试\n");
    printf("=====================================\n");
    
    test_dynamic_array_set_memory_leak();
    test_type_safe_functions_correct_behavior();
    
    printf("\n总结：\n");
    printf("- array_set函数在动态数组上会导致内存泄漏\n");
    printf("- array_set_int/array_set_double函数正确处理内存\n");
    printf("- 需要修复array_set函数以保持一致性\n");
    
    return 0;
}
